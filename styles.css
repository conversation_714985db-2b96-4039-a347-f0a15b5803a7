/* MICROVIP88 Custom Styles */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: #ffffff;
    line-height: 1.6;
    min-height: 100vh;
}

/* Container Styles */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Header and Top Container */
.container-top {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    border-bottom: 2px solid #ffd700;
    padding: 10px 0;
}

/* Language Dropdown */
.dropdown.lang {
    position: relative;
}

.dropdown-toggle {
    background: transparent;
    border: 2px solid #ffd700;
    color: #ffd700;
    padding: 8px 15px;
    border-radius: 25px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.dropdown-toggle:hover {
    background: #ffd700;
    color: #1a1a2e;
    transform: translateY(-2px);
}

.dropdown-menu {
    background: rgba(26, 26, 46, 0.95);
    border: 1px solid #ffd700;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.dropdown-item {
    color: #ffffff;
    padding: 10px 15px;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background: #ffd700;
    color: #1a1a2e;
}

/* Login Form Styles */
.form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 25px;
    color: #ffffff;
    padding: 12px 20px;
    transition: all 0.3s ease;
}

.form-control:focus {
    background: rgba(255, 255, 255, 0.2);
    border-color: #ffd700;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
    outline: none;
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

/* Button Styles */
.btn-robotic {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    border: none;
    color: #1a1a2e;
    font-weight: bold;
    padding: 12px 25px;
    border-radius: 25px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-robotic:hover {
    background: linear-gradient(45deg, #ffed4e, #ffd700);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(255, 215, 0, 0.4);
}

.btn-pill {
    border-radius: 50px;
}

/* Main Menu Styles */
.main-menu {
    background: rgba(0, 0, 0, 0.9);
    padding: 15px 0;
    border-bottom: 3px solid #ffd700;
}

.logo-holder {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
}

.logo {
    max-height: 60px;
    filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.5));
}

/* Navigation Menu */
.inner {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    list-style: none;
    gap: 20px;
    padding: 0;
}

.inner li {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.inner li:hover {
    background: rgba(255, 215, 0, 0.2);
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(255, 215, 0, 0.3);
}

.inner li a {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px 20px;
    text-decoration: none;
    color: #ffffff;
    transition: all 0.3s ease;
}

.inner li a img {
    width: 40px;
    height: 40px;
    margin-bottom: 8px;
    filter: brightness(1.2);
}

.inner li a p {
    margin: 0;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Navigation Buttons */
.button {
    background: rgba(255, 215, 0, 0.2);
    border: 2px solid #ffd700;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.button:hover {
    background: #ffd700;
    color: #1a1a2e;
    transform: scale(1.1);
}

/* Carousel Styles */
.carousel-item img {
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.carousel-control-prev,
.carousel-control-next {
    width: 5%;
    opacity: 0.8;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    opacity: 1;
}

/* Hot Games Section */
.container-hotgame {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.container-hotgame p {
    color: #ffd700;
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 15px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-refresh {
    width: 100%;
    background: linear-gradient(45deg, #ff6b6b, #ee5a52);
    border: none;
    color: white;
    padding: 10px;
    border-radius: 25px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.btn-refresh:hover {
    background: linear-gradient(45deg, #ee5a52, #ff6b6b);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
}

/* Announcement Section */
.announcement {
    background: rgba(255, 215, 0, 0.1);
    border: 2px solid #ffd700;
    border-radius: 15px;
    padding: 15px;
    backdrop-filter: blur(10px);
}

.announcement i {
    color: #ffd700;
    font-size: 24px;
    margin-right: 15px;
}

.announcement marquee {
    color: #ffffff;
    font-weight: bold;
    font-size: 14px;
}

/* Jackpot Section */
.jackpot-container {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
    color: #1a1a2e;
}

.jackpot-header {
    font-size: 18px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.jackpot-content {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
}

.jackpot-content span[data-name="jackpot"] {
    font-size: 32px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* Contact Section */
.contactus {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.contactus .title {
    color: #ffd700;
    font-size: 16px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Footer Styles */
.footer {
    background: rgba(0, 0, 0, 0.9);
    margin-top: 50px;
    padding: 40px 0 20px;
    border-top: 3px solid #ffd700;
}

.footer-logo {
    text-align: center;
    padding: 20px 0;
}

.footer-logo img {
    max-height: 80px;
    filter: drop-shadow(0 0 15px rgba(255, 215, 0, 0.5));
}

.provider-title .h3 {
    color: #ffd700;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 10px;
}

/* Modal Styles */
.modal-content {
    background: rgba(26, 26, 46, 0.95);
    border: 2px solid #ffd700;
    border-radius: 15px;
    backdrop-filter: blur(15px);
}

.modal-header {
    border-bottom: 1px solid rgba(255, 215, 0, 0.3);
}

.modal-title {
    color: #ffd700;
    font-weight: bold;
}

.modal-body {
    color: #ffffff;
}

.close {
    color: #ffd700;
    opacity: 1;
    font-size: 28px;
}

.close:hover {
    color: #ffffff;
}

/* Responsive Design */
@media (max-width: 768px) {
    .inner {
        gap: 10px;
    }
    
    .inner li a {
        padding: 10px 15px;
    }
    
    .inner li a img {
        width: 30px;
        height: 30px;
    }
    
    .inner li a p {
        font-size: 10px;
    }
    
    .jackpot-content {
        font-size: 18px;
    }
    
    .jackpot-content span[data-name="jackpot"] {
        font-size: 24px;
    }
}

/* Animation Classes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Glow Effect */
.glow {
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

/* Hover Effects */
.hover-lift:hover {
    transform: translateY(-5px);
    transition: transform 0.3s ease;
}

/* Registration Form Styles */
.sga-input-icon-group {
    position: relative;
    margin-bottom: 15px;
}

.sga-input-icon-group input,
.sga-input-icon-group select {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 25px;
    color: #ffffff;
    padding: 12px 20px 12px 50px;
    width: 100%;
    transition: all 0.3s ease;
}

.sga-input-icon-group input:focus,
.sga-input-icon-group select:focus {
    background: rgba(255, 255, 255, 0.2);
    border-color: #ffd700;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
    outline: none;
}

.sga-input-icon-group::before {
    content: attr(data-icon);
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    position: absolute;
    left: 18px;
    top: 50%;
    transform: translateY(-50%);
    color: #ffd700;
    z-index: 1;
}

/* Error Messages */
.errmsg {
    color: #ff6b6b;
    font-size: 12px;
    margin-top: 5px;
    margin-left: 15px;
}

/* Container Bank Styles */
.container-bank {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid rgba(255, 215, 0, 0.2);
}

.container-bank .title {
    color: #ffd700;
    font-weight: bold;
    margin-bottom: 15px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Captcha Styles */
.input-captcha {
    display: flex;
    align-items: center;
    gap: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 15px;
    border: 2px solid rgba(255, 215, 0, 0.3);
}

.input-captcha img {
    border-radius: 8px;
    border: 1px solid rgba(255, 215, 0, 0.5);
}

.input-captcha input {
    flex: 1;
    background: transparent;
    border: none;
    color: #ffffff;
    padding: 10px;
    font-size: 16px;
}

.input-captcha input:focus {
    outline: none;
}

/* Table Styles */
.sga-table {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.sga-table thead th {
    background: rgba(255, 215, 0, 0.2);
    color: #ffd700;
    font-weight: bold;
    padding: 15px;
    border: none;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.sga-table tbody td {
    color: #ffffff;
    padding: 12px 15px;
    border-top: 1px solid rgba(255, 215, 0, 0.1);
}

.sga-table tbody tr:hover {
    background: rgba(255, 215, 0, 0.1);
}

/* Loading Styles */
.loading-bar {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffd700;
}

.loading-text {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #1a1a2e;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.loading-text:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
}

/* Progress Bar */
.progress {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 25px;
    overflow: hidden;
    height: 20px;
}

.progress-bar {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    height: 100%;
    transition: width 0.3s ease;
}

.progress-bar.indeterminate {
    animation: indeterminate 2s infinite linear;
}

@keyframes indeterminate {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Page Popup Styles */
.pagepopup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: none;
}

.pagepopup-main {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(26, 26, 46, 0.95);
    border: 2px solid #ffd700;
    border-radius: 15px;
    padding: 20px;
    z-index: 10000;
    backdrop-filter: blur(15px);
    display: none;
}

.pagepopup-main [data-name="close"] {
    position: absolute;
    top: 10px;
    right: 15px;
    color: #ffd700;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.pagepopup-main [data-name="close"]:hover {
    color: #ffffff;
    transform: scale(1.2);
}

.pagepopupimg {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
}

/* Utility Classes */
.text-gold {
    color: #ffd700;
}

.bg-dark-transparent {
    background: rgba(0, 0, 0, 0.8);
}

.border-gold {
    border: 2px solid #ffd700;
}

.shadow-gold {
    box-shadow: 0 5px 20px rgba(255, 215, 0, 0.3);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #ffed4e, #ffd700);
}
