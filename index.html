
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8" />
            <meta name="page-culture" content="id-ID" langculture="id-ID" langcode="id" />
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta name="format-detection" content="telephone=no" />
            <meta name="identifier" content="B84C827CE42C4057AFCFE616AF37E5E9">
            <meta name="google-site-verification" content="-X7D3E_76e86eKepe_d_suGfRNYhp7DRvSpOizQoQGQ" />
<meta name="google-site-verification" content="mOtHNwC2jycsH1E8QXEdWIVqDrGfoOgTGu9Tmdq3E3g" />
<link rel="canonical" href="https://microvip88.lat/" />
<link rel="amphtml" href="https://microvip88.host/"/>

            <link href="https://sga-cdn-hxg6b2d7ctb2c0eu.z02.azurefd.net/agent-websites/286/medialibrary/images/286_80806ff839534164a1ad60563ab0f4d0.png" rel="icon" type="image/png" />

            <title>MICROVIP88 # Situs Resmi Slot Pragmatic Online Paling Gacor</title>

<link href="/Contents/Styles/htmlhead/theme/robotic/dark/css?v=ICQperSCVULA2GzcGfSusny2lwqydvFNsT1BMdsm8zE1" rel="stylesheet"/>
<link href="/Contents/Styles/htmlhead/state/loggedout/theme/robotic/dark/css?v=NvuZ0ms1zMxB1xuzduyD-5LkFWUhAIpKkdZjDMWsWok1" rel="stylesheet"/>
<script src="/Contents/Scripts/htmlhead/theme/robotic/js?v=6Sj1WOmACU5Rje9rT6b20cCv9yOMJ3LyGNOwX5dxfTk1"></script>

            
<link href="/Contents/Styles/home/<USER>/themestyle/default/theme/robotic/dark/css?v=FKG9a9uCCwJb-RmQZfwo1PsTCO0ZyQavGYJKdMmV0LY1" rel="stylesheet"/>

        
    
            
<script src="/Contents/Scripts/desktop/home/<USER>/theme/robotic/themestyle/default/js?v=YLpLsSXoIJskR9uRYM0ytouJMSIV1QhP3jx-Mt0c63A1"></script>

        
    

            <script src="/contents/scripts/globalize/cultures/globalize.culture.id-ID.js" type="text/javascript"></script>
        </head>
        <body>
            <div class="container-top">
            


<div class="container">
    <div class="row pt-3">
        <div class="col-1 pl-0 pt-0">
            <div class="dropdown lang">
                <a href="#" class="dropdown-toggle" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <span><i class="flag-icon flag-icon-id"></i></span>
                </a>
                <div class="dropdown-menu">
                    <a href="/Home/ChangeLanguage?SystemLangId=1&amp;RetUrl=%2F" class="dropdown-item">
                        <i class="flag-icon flag-icon-us flag-icon-bordered"></i><span>English</span>
                    </a>
                    <a href="/Home/ChangeLanguage?SystemLangId=2&amp;RetUrl=%2F" class="dropdown-item">
                        <i class="flag-icon flag-icon-id flag-icon-bordered"></i><span>Bahasa Indonesia</span>
                    </a>
                                    <hr />
                    <a href="/Home/ChangeCurrency?CurrencyId=-1&amp;RetUrl=%2F" class="dropdown-item all selected">
                        <i class="fas fa-globe"></i><span>Semua</span>
                    </a>
                    <a href="/Home/ChangeCurrency?CurrencyId=360&amp;RetUrl=%2F" class="dropdown-item">
                        <span>IDR</span> - <span>Rp</span>
                    </a>
                </div>
            </div>
        </div>

            <div class="col-md-1 col-lg-3 d-sm-none d-md-block"></div>
            <div class="col-11 col-md-10 col-lg-8">
                <div class="row" data-name="logincontainer">
                    <div class="col-12 col-md-6">
                        <div class="row">
                            <div class="col-6">
                                <input type="text" class="form-control" data-name="username" placeholder="Username">
                            </div>
                            <div class="col-6">
                                <input type="password" class="form-control" data-name="password" placeholder="Password">
                                <i class="fa fa-fw fa-eye-slash field-icon toggle-password-desktop" button type="button"></i>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-md-6">
                        <div class="row">
                            <div class="col-6">
                                <button class="btn btn-robotic btn-pill login" onclick="javascript:login(this, 'https://microvip88.lat/')">Masuk</button>
                            </div>
                            <div class="col-6 pr-0">
                                <button class="btn btn-robotic btn-pill" onclick="javascript:openRegDlg();"> Daftar </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </div>
</div>
            </div>
            <div class="main-menu">
                <div class="container">
                    <div class="logo-holder">
                        <a href="/">
                            <img src="https://sga-cdn-hxg6b2d7ctb2c0eu.z02.azurefd.net/agent-websites/286/medialibrary/images/286_bc7c0ba3269d41efade08f4a572761d4.webp" alt="Microvip88" class="img-fluid align-middle logo">
                        </a>
                    </div>
                    <div class="col-lg-10 col-xl-10 col-md-10">
                        <div id="left" class="button btn-left">
                            <span class="fas fa-chevron-left" data-name="btnleft"></span>
                        </div>
                        <ul class="inner">
                            <li data-id="home">
                                <a href="/">
                                    <img src="https://sga-cdn-hxg6b2d7ctb2c0eu.z02.azurefd.net/agent-websites/website/Themes/Robotic/Desktop/Contents/Images/Dark/MainMenu/ic_home.webp" alt="Home">
                                    <p>Home</p>
                                </a>
                            </li>
                                                        <li data-id="backtohome">
                                <a href="/Home/BackToHome?ForwardUrl=https%3A%2F%2Fmicrovip88.lat%2F">
                                    <p>Balik</p>
                                </a>
                            </li>
                                                            <li data-id="2" >
                                    <a href="/Game/ProviderLobby?MGId=2">
                                        <img src="https://sga-cdn-hxg6b2d7ctb2c0eu.z02.azurefd.net/agent-websites/website/Themes/Robotic/Desktop/Contents/Images/Dark/MainMenu/ic_slot.webp"  alt="SLOTS">
                                                                            <p>SLOTS</p>
                                    </a>
                                </li>
                                <li data-id="3" >
                                    <a href="/Game/ProviderLobby?MGId=3">
                                        <img src="https://sga-cdn-hxg6b2d7ctb2c0eu.z02.azurefd.net/agent-websites/website/Themes/Robotic/Desktop/Contents/Images/Dark/MainMenu/ic_lotto.webp"  alt="TOGEL">
                                                                            <p>TOGEL</p>
                                    </a>
                                </li>
                                <li data-id="4" >
                                    <a href="/Game/ProviderLobby?MGId=4">
                                        <img src="https://sga-cdn-hxg6b2d7ctb2c0eu.z02.azurefd.net/agent-websites/website/Themes/Robotic/Desktop/Contents/Images/Dark/MainMenu/ic_sport.webp"  alt="SPORT">
                                                                            <p>SPORT</p>
                                    </a>
                                </li>
                                <li data-id="7" >
                                    <a href="/Game/ProviderLobby?MGId=7">
                                        <img src="https://sga-cdn-hxg6b2d7ctb2c0eu.z02.azurefd.net/websites/medialibrary/images/3f93e376220a400495f6b73d5a866025.webp"  alt="Live Casino">
                                                                            <p>Live Casino</p>
                                    </a>
                                </li>
                                <li data-id="9" >
                                    <a href="/Game/ProviderLobby?MGId=9">
                                        <img src="https://sga-cdn-hxg6b2d7ctb2c0eu.z02.azurefd.net/agent-websites/website/Themes/Robotic/Desktop/Contents/Images/Dark/MainMenu/ic_gaming.webp"  alt="E-GAMES">
                                                                            <p>E-GAMES</p>
                                    </a>
                                </li>
                                                        <li>
                                <a href="/Home/Promo">
                                    <img src="https://sga-cdn-hxg6b2d7ctb2c0eu.z02.azurefd.net/agent-websites/website/Themes/Robotic/Desktop/Contents/Images/Dark/MainMenu/ic_promo.webp" alt="Promosi" />
                                    <p>Promosi</p>
                                </a>
                            </li>
                                                        <li>
                                <a href="/Home/SGALottoResult">
                                    <img src="https://sga-cdn-hxg6b2d7ctb2c0eu.z02.azurefd.net/agent-websites/website/Themes/Robotic/Desktop/Contents/Images/Dark/MainMenu/ic_lotto_result.webp" alt="Hasil Keluaran" />
                                    <p>Hasil Keluaran</p>
                                </a>
                            </li>
                                                    </ul>
                        <div id="right" class="button btn-right">
                            <span class="fas fa-chevron-right" data-name="btnright"></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="container top-menu-container">
                <div class="row"></div>
            </div>

            <div class="body">
                


    <div class="container mt-3">
        <div class="row">
            <div class="col-xl-9 p-0 pe-0 pe-xl-3">
                <div id="carouselSlides" class="carousel slide" data-ride="carousel">
                    <div class="carousel-inner">
                        <div class="carousel-item active"><img src="https://sga-cdn-hxg6b2d7ctb2c0eu.z02.azurefd.net/agent-websites/286/medialibrary/images/286_a41bf793a1cf4fe59ceabe20a9735531.png" title="MICROVIP88.png" class="w-100" /></div>
                    </div>
                    <a class="carousel-control-prev" href="#carouselSlides" role="button" data-slide="prev">
                        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                        <span class="sr-only">Previous</span>
                    </a>
                    <a class="carousel-control-next" href="#carouselSlides" role="button" data-slide="next">
                        <span class="carousel-control-next-icon" aria-hidden="true"></span>
                        <span class="sr-only">Next</span>
                    </a>
                </div>
            </div>
            <div class="col-xl-3 container-hotgame">
                <p>HOT GAMES</p>
                <div>
                    <div class="load-status">
                        <button type="button" class="btn btn-robotic btn-primary btn-refresh">Segarkan</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="container announcement mt-3">
        <div>
            <div>
                <i class="fas fa-volume-up"></i>
            </div>
            <div>
                <marquee>Selamat Datang Di Situs Terpercaya MICROVIP88 || Link Resmi : WWW.MICROVIP88.ID || Tersedia Deposit Via Pulsa, E-wallet &amp; Semua Jenis Bank Indonesia || Bank Online 24 Jam ( Kecuali Gangguan ) || Selamat Bermain dan Salam JP! </marquee>
            </div>
        </div>
    </div>
    <div class="container mt-3">
        <div class="row jackpot-container">
            <div class="col-3">
                <div class="jackpot-header">MEGA JACKPOT</div>
            </div>
            <div class="jackpot-content col-9">
                <div>

                    <span>IDR</span> <span data-name="jackpot">*********</span>
                </div>
            </div>
        </div>
    </div>
    <div class="container contactus mt-3">
        <div class="row">
            <div class="col-lg-3 col-xl-2 text-center">
                <span class="title">HUBUNGI KAMI</span>
            </div>
            <div class="col-lg-9 col-xl-10">
                <div class="row content">
                </div>
            </div>
        </div>
    </div>




<div id="dlgTournament" class="tournament-details modal fade" role="dialog" data-name="dialog">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Informasi Turnamen</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col main-body" data-name="tournamentbody"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    addStrRes('abbr_day', 'hr');
    addStrRes('abbr_hour', 'jam');
    addStrRes('abbr_minute', 'min');
    addStrRes('abbr_points', 'poin');
    addStrRes('abbr_second', 'dtk');
    addStrRes('all_games', 'Semua Game');
    addStrRes('all_membership_levels', 'Semua Level Membership');
    addStrRes('days', 'Hari');
    addStrRes('end_date', 'Tanggal Akhir');
    addStrRes('exclude_purchased_promo_game', 'Kecuali Promo Game yang Dibeli');
    addStrRes('friday', 'Jumat');
    addStrRes('games_in_this_tournament', 'Game di Turnamen Ini');
    addStrRes('membership_level', 'Membership Level');
    addStrRes('monday', 'Senin');
    addStrRes('msg_all_games_in_provider_xxx', 'Semua game di provider {0}');
    addStrRes('msg_all_games_in_provider_xxx_with_category_xxx', 'Semua game di provider {0} dengan kategori {1}');
    addStrRes('no', 'Tidak');
    addStrRes('prize_value', 'Nilai Hadiah');
    addStrRes('rank', 'Peringkat');
    addStrRes('reward', 'Hadiah');
    addStrRes('saturday', 'Sabtu');
    addStrRes('start_date', 'Tanggal Awal');
    addStrRes('sunday', 'Minggu');
    addStrRes('thursday', 'Kamis');
    addStrRes('tickets', 'tiket');
    addStrRes('tournament_ended', 'Turnamen Sudah Selesai');
    addStrRes('tournament_ends_in', 'Turnamen Berakhir Dalam');
    addStrRes('tournament_information', 'Informasi Turnamen');
    addStrRes('tournament_repeats_every_xxx', 'Turnament Diulang Setiap {0}');
    addStrRes('tournament_repeats_everyday', 'Turnamen Diulang Setiap Hari');
    addStrRes('tournament_repeats_monthly', 'Turnamen Diulang Setiap Bulan');
    addStrRes('tournament_repeats_monthly_for_the_first_xxx_days', 'Turnamen Diulang Setiap Bulan Untuk {0} Hari Pertama');
    addStrRes('tournament_repeats_weekly', 'Turnamen Diulang Setiap Minggu');
    addStrRes('tournament_repeats_yearly', 'Turnamen Diulang Setiap Tahun');
    addStrRes('tournament_repeats_yearly_from_xxx_to_xxx', 'Turnamen Diulang Setiap Tahun Dari {0} Ke {1}');
    addStrRes('tournament_starts_in', 'Turnamen Dimulai Dalam');
    addStrRes('tuesday', 'Selasa');
    addStrRes('wednesday', 'Rabu');
    addStrRes('yes', 'Ya');
</script><div id="dlgTournamentRank" class="modal fade" role="dialog">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Daftar Peringkat</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body">
                <input type="hidden" data-name="pageno" value="1" />
                <div data-mainscrollable>
                    <table class="table table-striped table-bordered sga-table no-footer text-center" data-name="ranklist">
                        <thead>
                            <tr>
                                <th>Peringkat</th>
                                <th>Nama User</th>
                                <th data-name="tournamenttype">Turnover</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <div class="btnloadmore" data-name="loadmore">
                        <div class="loading-text">Muat Lagi</div>
                        <div class="loading-bar" style="display:none;"><i class="fas fa-spinner fa-spin"></i></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
    <div class="load-more-btn">
    <div class="loading-text">Muat Lagi</div>
    <div class="loading-bar" style="display:none;"><i class="fas fa-spinner fa-spin"></i></div>
</div>
<script>
    addStrRes('load_more_records', 'Muat Record Lagi');
    addStrRes('load_records', 'Muat Record');
</script>
</div>

<script>
    addStrRes('msg_no_rank_list_is_available_for_this_tournament', 'Tidak ada daftar peringkat yang tersedia untuk turnamen ini');
</script><script>
    addStrRes('bronze_spin_ticket', 'Spin Tiket Bronze');
    addStrRes('cash', 'Kas');
    addStrRes('custom', 'Hadiah Special');
    addStrRes('gold_spin_ticket', 'Spin Tiket Gold');
    addStrRes('membership_point', 'Membership Poin');
    addStrRes('platinum_spin_ticket', 'Spin Tiket Platinum');
    addStrRes('rolling_cash', 'Kas Rolling');
    addStrRes('silver_spin_ticket', 'Spin Tiket Silver');
</script><script>
    addStrRes('minimum_loss_amount_from_win_loss', 'Minimal Kekalahan (Dari Menang/Kalah)');
    addStrRes('minimum_losing_amount', 'Minimal Kekalahan');
    addStrRes('minimum_turnover_amount', 'Minimal Turnover');
    addStrRes('minimum_turnover_amount_with_tie', 'Minimal Turnover (Termasuk Seri)');
    addStrRes('minimum_win_amount_from_win_loss', 'Minimal Kemenangan (Dari Menang/Kalah)');
    addStrRes('minimum_winning_amount', 'Minimal Kemenangan');
    addStrRes('total_loss_amount_from_win_loss', 'Jumlah Total Kekalahan (Dari Menang/Kalah)');
    addStrRes('total_losing_amount', 'Jumlah Total Kekalahan');
    addStrRes('total_turnover_amount', 'Jumlah Total Turnover');
    addStrRes('total_turnover_amount_with_tie', 'Jumlah Total Turnover (Termasuk Seri)');
    addStrRes('total_win_amount_from_win_loss', 'Jumlah Total Kemenangan (Dari Menang/Kalah)');
    addStrRes('total_winning_amount', 'Jumlah Total Kemenangan');
</script>    <script>
        addStrRes('abbr_day', 'hr');
        addStrRes('abbr_hour', 'jam');
        addStrRes('abbr_minute', 'min');
        addStrRes('abbr_points', 'poin');
        addStrRes('abbr_second', 'dtk');
        addStrRes('tickets', 'tiket');

        const PROMO_TOURNAMENTS = [];
        const REC_CURS = [];
        const REC_GAMES = [];
        const REC_GAMECATS  = [];
        const REC_LEVELS = [{"Id":1,"Name":"Brown Member"},{"Id":2,"Name":"Silver Member"},{"Id":3,"Name":"Gold Member"},{"Id":4,"Name":"Platinum Member"}];
        const REC_PROVIDERS = [];
        const REC_TIMEZONES = [];
        const SPANCUR = '<span data-type="currency" data-currencysymbol="Rp" data-decimalprecision="0">{0}</span>';
    </script>




<script>
    addStrRes('bronze_spin_ticket', 'Spin Tiket Bronze');
    addStrRes('cash', 'Kas');
    addStrRes('custom', 'Hadiah Special');
    addStrRes('gold_spin_ticket', 'Spin Tiket Gold');
    addStrRes('membership_point', 'Membership Poin');
    addStrRes('platinum_spin_ticket', 'Spin Tiket Platinum');
    addStrRes('rolling_cash', 'Kas Rolling');
    addStrRes('silver_spin_ticket', 'Spin Tiket Silver');
</script><script>
    addStrRes('minimum_loss_amount_from_win_loss', 'Minimal Kekalahan (Dari Menang/Kalah)');
    addStrRes('minimum_losing_amount', 'Minimal Kekalahan');
    addStrRes('minimum_turnover_amount', 'Minimal Turnover');
    addStrRes('minimum_turnover_amount_with_tie', 'Minimal Turnover (Termasuk Seri)');
    addStrRes('minimum_win_amount_from_win_loss', 'Minimal Kemenangan (Dari Menang/Kalah)');
    addStrRes('minimum_winning_amount', 'Minimal Kemenangan');
    addStrRes('total_loss_amount_from_win_loss', 'Jumlah Total Kekalahan (Dari Menang/Kalah)');
    addStrRes('total_losing_amount', 'Jumlah Total Kekalahan');
    addStrRes('total_turnover_amount', 'Jumlah Total Turnover');
    addStrRes('total_turnover_amount_with_tie', 'Jumlah Total Turnover (Termasuk Seri)');
    addStrRes('total_win_amount_from_win_loss', 'Jumlah Total Kemenangan (Dari Menang/Kalah)');
    addStrRes('total_winning_amount', 'Jumlah Total Kemenangan');
</script><div id="dlgGlobalTournament" class="tournament-details modal fade" role="dialog" data-name="dialog">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Informasi Turnamen</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col main-body" data-name="tournamentbody"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    addStrRes('abbr_day', 'hr');
    addStrRes('abbr_hour', 'jam');
    addStrRes('abbr_minute', 'min');
    addStrRes('abbr_points', 'poin');
    addStrRes('abbr_second', 'dtk');
    addStrRes('all_games', 'Semua Game');
    addStrRes('days', 'Hari');
    addStrRes('end_date', 'Tanggal Akhir');
    addStrRes('exclude_purchased_promo_game', 'Kecuali Promo Game yang Dibeli');
    addStrRes('friday', 'Jumat');
    addStrRes('games_in_this_tournament', 'Game di Turnamen Ini');
    addStrRes('monday', 'Senin');
    addStrRes('msg_all_games_in_provider_xxx', 'Semua game di provider {0}');
    addStrRes('msg_all_games_in_provider_xxx_with_category_xxx', 'Semua game di provider {0} dengan kategori {1}');
    addStrRes('no', 'Tidak');
    addStrRes('prize_value', 'Nilai Hadiah');
    addStrRes('rank', 'Peringkat');
    addStrRes('reward', 'Hadiah');
    addStrRes('saturday', 'Sabtu');
    addStrRes('start_date', 'Tanggal Awal');
    addStrRes('sunday', 'Minggu');
    addStrRes('thursday', 'Kamis');
    addStrRes('tickets', 'tiket');
    addStrRes('tournament_ended', 'Turnamen Sudah Selesai');
    addStrRes('tournament_ends_in', 'Turnamen Berakhir Dalam');
    addStrRes('tournament_information', 'Informasi Turnamen');
    addStrRes('tournament_repeats_every_xxx', 'Turnament Diulang Setiap {0}');
    addStrRes('tournament_repeats_everyday', 'Turnamen Diulang Setiap Hari');
    addStrRes('tournament_repeats_monthly', 'Turnamen Diulang Setiap Bulan');
    addStrRes('tournament_repeats_monthly_for_the_first_xxx_days', 'Turnamen Diulang Setiap Bulan Untuk {0} Hari Pertama');
    addStrRes('tournament_repeats_weekly', 'Turnamen Diulang Setiap Minggu');
    addStrRes('tournament_repeats_yearly', 'Turnamen Diulang Setiap Tahun');
    addStrRes('tournament_repeats_yearly_from_xxx_to_xxx', 'Turnamen Diulang Setiap Tahun Dari {0} Ke {1}');
    addStrRes('tournament_starts_in', 'Turnamen Dimulai Dalam');
    addStrRes('tuesday', 'Selasa');
    addStrRes('wednesday', 'Rabu');
    addStrRes('yes', 'Ya');
</script><div id="dlgGlobalTournamentRank" class="modal fade" role="dialog">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Daftar Peringkat</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body">
                <input type="hidden" data-name="pageno" value="1" />
                <div data-mainscrollable>
                    <table class="table table-striped table-bordered sga-table no-footer text-center" data-name="ranklist">
                        <thead>
                            <tr>
                                <th>Peringkat</th>
                                <th>Web</th>
                                <th>Nama User</th>
                                <th data-name="tournamenttype">Turnover</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                    <div class="btnloadmore" data-name="loadmore">
                        <div class="loading-text">Muat Lagi</div>
                        <div class="loading-bar" style="display:none;"><i class="fas fa-spinner fa-spin"></i></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
    <div class="load-more-btn">
    <div class="loading-text">Muat Lagi</div>
    <div class="loading-bar" style="display:none;"><i class="fas fa-spinner fa-spin"></i></div>
</div>
<script>
    addStrRes('load_more_records', 'Muat Record Lagi');
    addStrRes('load_records', 'Muat Record');
</script>
</div>

<script>
    addStrRes('msg_no_rank_list_is_available_for_this_tournament', 'Tidak ada daftar peringkat yang tersedia untuk turnamen ini');
</script>    <script>
        addStrRes('abbr_day', 'hr');
        addStrRes('abbr_hour', 'jam');
        addStrRes('abbr_minute', 'min');
        addStrRes('abbr_points', 'poin');
        addStrRes('abbr_second', 'dtk');
        addStrRes('tickets', 'tiket');

        const PROMO_GLOBAL_TOURNAMENTS = [];
        const REC_GLOBAL_CURS = [];
        const REC_GLOBAL_GAMES = [];
        const REC_GLOBAL_GAMECATS  = [];
        const REC_GLOBAL_PROVIDERS = [];
        const REC_GLOBAL_TIMEZONES = [];
    </script>


            </div>

            <div class="footer">
                <div class="container">
                    <div class="row">
                        <div class="col-12">
                            MICROVIP88 menyediakan situs resmi SGA dengan berbagai game slot pragmatic online terbaru yang paling gacor dan viral diseluruh indonesia yang memberikan kemenangan maxwin setiap hari.
                            <hr class="m-0">
                        </div>
                    </div>

                    <div class="row provider-title">
                        <div class="col-12">
                            <div class="h3">Provider Game</div>
                            <div>Platform</div>
                        </div>
                    </div>
                    <div class="row provider"></div>
                </div>
                <div class="footer-logo">
                    <img alt="Microvip88" class="img-fluid" src="https://sga-cdn-hxg6b2d7ctb2c0eu.z02.azurefd.net/agent-websites/website/Themes/Robotic/Desktop/Contents/Images/ic_logo_with_name.webp" />
                </div>
                            </div>

<input name="__RequestVerificationToken" type="hidden" value="NKKISm44QtcrAp9ezKgr0VzDnFkRkhXE0U1PD7FHyfW2FSSC6wfHCQ9VeaGNZnGqqedsGIiaWqXpTyNlfck8MAvZ85pZK5IpxEXz0dZ3gQ81" />                <div id="dlgRegister" class="modal fade" role="dialog">
                    <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Daftar</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            </div>
                            <div class="modal-body">
                                

<div class="container" data-name="registercontainer">
    <div class="title">User Login</div>
    <div class="row">
        <div class="col-12">
            <div class="sga-input-icon-group" data-icon="&#xf007;">
                <input type="text" data-name="username" maxlength="12" placeholder="Username (3 - 12  Karakter)">
            </div>
            <div class="errmsg"></div>
        </div>
    </div>
    <div class="row">
        <div class="col-12 col-lg-6">
            <div class="sga-input-icon-group" data-icon="&#xf2c3;">
                <input type="text" data-name="firstname" maxlength="64" placeholder="Nama Depan">
            </div>
            <div class="errmsg"></div>
        </div>
        <div class="col-12 col-lg-6">
            <div class="sga-input-icon-group" data-icon="&#xf2c3;">
                <input type="text" data-name="lastname" maxlength="64" placeholder="Nama Belakang">
            </div>
            <div class="errmsg"></div>
        </div>
    </div>
    <div class="row">
        <div class="col-12 col-lg-6">
            <div class="sga-input-icon-group" data-icon="&#xf023;">
                <input type="password" class="form-control" data-name="password" maxlength="64" placeholder="Password">
                <i class="fa fa-fw fa-eye-slash field-icon toggle-password-desktop" button type="button"></i>
            </div>
            <div class="errmsg"></div>
        </div>
        <div class="col-12 col-lg-6">
            <div class="sga-input-icon-group" data-icon="&#xf023;">
                <input type="password" class="form-control" data-name="confirmpassword" maxlength="64" placeholder="Verifikasi Password">
                <i class="fa fa-fw fa-eye-slash field-icon toggle-password-desktop" button type="button"></i>
            </div>
            <div class="errmsg"></div>
        </div>
    </div>

    <div class="container-bank">
        <div class="row">
            <span class="col-6 title">Bank Anda</span>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="sga-input-icon-group" data-icon="&#xf19c;">
                    <select data-name="bankaccs" required>
                        <option value="" selected disabled hidden>Pilih Rekening Bank</option>
                            <optgroup label="Bank" data-paymentmethod="2">
                                <option value="2_1">BCA</option>
                                <option value="2_2">MANDIRI</option>
                                <option value="2_3">BNI</option>
                                <option value="2_4">BRI</option>
                                <option value="2_5">OCBC</option>
                                <option value="2_6">DANA</option>
                                <option value="2_7">OVO</option>
                                <option value="2_8">GOPAY</option>
                                <option value="2_9">LINKAJA</option>
                                <option value="2_10">SAKUKU</option>
                            </optgroup>
                                                    <optgroup label="Dompet Digital" data-paymentmethod="3">
                                <option value="3_1">DANA</option>
                                <option value="3_2">OVO</option>
                                <option value="3_3">GOPAY</option>
                                <option value="3_4">LINKAJA</option>
                            </optgroup>
                                                                    </select>
                </div>
                <div class="errmsg"></div>
            </div>
        </div>
        <div class="row">
            <div class="col-12 col-lg-6">
                <div class="sga-input-icon-group" data-icon="&#xf007;">
                    <input type="text" data-name="accname" maxlength="64" placeholder="Nama Rekening">
                </div>
                <div class="errmsg"></div>
            </div>
            <div class="col-12 col-lg-6">
                <div class="sga-input-icon-group" data-icon="&#xf555;">
                    <input type="text" data-name="accnumber" maxlength="26" placeholder="Nomor Rekening">
                </div>
                <div class="errmsg"></div>
            </div>
            <div class="col-12 col-lg-6">
                <div class="sga-input-icon-group" data-icon="&#xf6ff;">
                    <select class="form-control" data-name="cwnetworktypes" required>
                        <option value="" selected disabled hidden>Pilih Jenis Jaringan</option>
                    </select>
                </div>
                <div class="errmsg"></div>
            </div>
            <div class="col-12 col-lg-6">
                <div class="sga-input-icon-group" data-icon="&#xf555;">
                    <input type="text" data-name="cwaddress" maxlength="128" placeholder="Alamat Dompet">
                </div>
                <div class="errmsg"></div>
            </div>
        </div>
    </div>

    <div class="title">Kontak Anda</div>
    <div class="row">
        <div class="col-12 col-lg-6">
            <div class="sga-input-icon-group" data-icon="&#xf0e0;">
                <input type="text" data-name="email" maxlength="128" placeholder="Email">
            </div>
            <div class="errmsg"></div>
        </div>
        <div class="col-12 col-lg-6">
            <div class="sga-input-icon-group" data-icon="&#xf87b;">
                <span data-name="mobileprefix">+62</span>
                <input type="text" data-name="mobile" maxlength="16" placeholder="812xxxxxxxx (Nomor HP)">
            </div>
            <div class="errmsg"></div>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="sga-input-icon-group" data-icon="&#xf500;">
                <input type="text" data-name="referral" placeholder="Kode Referral (Opsional)" value="" >
            </div>
            <div class="errmsg"></div>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="input-captcha">
                <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQYAAAA6CAYAAABS+vdeAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAABEBSURBVHhe7d1jsyzJ1wXwsW3btm2bMVaMbdu2PTG2bds27tfIf/zqiXqibvWuw+5zuiPyxYobc7oqKyux9trImnHGjBmTMjIyMqrIxJCRkdGCTAwZGRktyMSQkZHRgkwMGRkZLcjEkJGR0YJMDBkZGS3IxJCRkdGCTAwZGRktyMSQkZHRgp4khn///Td9++236Ysvvki//vpreE1GRsbQ0ZPE8Ntvv6Urr7wyHXbYYen8889Pt9xyS3rggQfSM888k95444306aefph9//LEgkOj+jP/DP//8E/49Y/Cw3n7++ef02muvpaeeeiq9+eab6euvv05//fVXeH23oyeJ4bvvvkvrrrtuGmeccdK4446bJppoojT33HOnlVZaKW211VbpgAMOSKeffnq67rrr0r333pueeOKJ9Oqrr6YPP/ywuHc0J+u///4riO2rr74qSOzZZ59N33//ffroo4/C6zsBKuvyyy9PJ5xwQvh7N8Ac2WzG6o8//ig2XXTdaOP3339P77zzTrrwwgvTWWedlTbbbLO07LLLFuvw6KOPTldffXV66KGH0ssvv1wYLO8StdNt6ElisKnmnXfeghj6wvjjj59mnnnmtNRSS6WNNtoo7bnnnun4449P11xzTbEZo7Y7gZ9++im9/fbb6bHHHksnnnhi0ZdVVlklLbTQQmmRRRZJK6+8clpxxRXTBRdckL755puwjXbAZkOSW2yxRZphhhkKMn344YfDa0cLxur+++9PO+20U0H022yzTbHZ9t577/Tggw92jcr5+++/0+eff57uuuuutOuuu6aZZpopXIMTTzxxsVbXX3/9dOihhxbGIGqv29BzxMDivv/++2mSSSYJJ2IgmHHGGQupF7XfbtiMt912W1pyySXTPPPMk6aeeupC4Uw44YRpvPHGK8hrggkmKP57uummSxtuuGF65JFHwraGA9aXBZttttn+f+w804L9888/w3tGEmT3tddemzbZZJM0yyyzpEknnbQYF321uaaYYoo0xxxzpB133DE9//zzYRsjBWPJXTjmmGPSMsssU/S1vsYiTDnllOnggw9OTz/9dNFG1Ha3oKeIQczgyy+/LOIL0cAPBFwPC2wkJuaXX34prNymm25aEIBnR32qwiagIiw6BBi1OxggJkpltdVWS1NNNdVYz9IfZHTeeeeF944EPv7443TmmWem1VdfvSBs71/tYxX6S+lQEHfeeWfY3kjg1ltvTeuss06aZpppCnKP+toE68C7iol1AyE3oaeIgX928803pyWWWCIc9IHAxCy//PJh++2EeAbrUFq/qC9N0EebeI011kh33HHHsIKoJC/rNvnkk4fPsrAXWGCBIgYT3d9p3H333YWSol6i/tWhv8Zzgw02SC+88ELYZich1rH11lv3S/Lmb7LJJguJg2o88MADC+Lv1gB5TxED/9Nmqw802Ew2oUXGqjS5GizS9ttvH7bfLiCwq666Kk0//fRhHwYKFlRMInrGQMH1YpX7IlNjxU+O7u8UBIEpP4E6cxf1qy+IHQkwR213CvqMyBiWqE+AtOabb760++67F2PKdYuuW3jhhYu2RjMQ3hd6ihgEDPmg0UCziFhYtF2Aca+99iok/JprrlksPlYRcVhQxx57bNh+u/Diiy+m9dZbr0+r4jdWElFZPGIQ9WtsGP0m9bkl0bP6A2J49913C5eh3n4JfTE2N910U9hGJyCSv8suu4RKwVzaODZY/bcSxkYQ17tF7XcCNvLGG29cWPyoT9aW3y+77LKixkbAee211w6v9Y433HBDVgztgADVoosu2jLIFhfZrY6hvNaGsJksnEcffTRdf/31hYU54ogjivRRtd12woI46qij+g2OsjospsUm9/3kk08WKa76dYJu++23X5G2i57XH9x30UUX9esL22iLLbZYEWmP2mk3pJGjjS9AJwOBOI477rii302KAnnceOONYfudAIPSF8FyMWR9yqI7GYgtt9wydCW9u7nv1lqSniEGG12u38KpD7LJEmAbqlVtJ6Ta+rJ04PdLL700WUACUKzG448/Xiib+rU2hkDX66+/Hj6vP/zwww9FmrbebgQkhDijdtoJ5HPIIYcUWYd6H6RwkbgxcR3yRPr164B1VhsQPaPdQLBSqFGfwd+5fdU0uHegCqgGAW8xh1JFek/v1q3ZiZ4ghnKRsHz1CQG+OKZmeZGHyRkN3+2tt94q4hdNi0eum3y+7777Wgp29H+55ZYL7+NOqL2oXj9QqIuQkozarQMJzTXXXB2vbVAUpGK17kZQBkiQK+Y6886vP+mkk8a6rgQiFd2vt98JSG+vtdZaYT8Qqs3PONUVAAVJoXrffffdtyAzanHnnXdOt99+e/F+1eu7BT1BDIJ5qhj5cNHEkO0WifjDPvvsUyykqkyP2mw3MP8ZZ5zRGP0HCwspPPfccy33l3GJ6D7FUCok6/f0BxsLWfUlf+uwWTtd28DKq06tuwgkt81TfbaNJj04++yzj3Wtfm677bbps88+G6vtTkEh0+KLLz5WH0pwaRC3+gTu7HvvvVcoNRmhejvcYVmiDz74IPy9W9ATxMC68tujSWkC68dCK4iRiutkRSH0ZfFLyHsvuOCCRZzk8MMPL/zo8n7kZ1GRq1V3iQQ9++yzB71RuV4WoQ1YjS/YUGRsFOwEUrfTtQ1qUVQB1p+tnyyqTVNe6x3UdNTVxZxzzlmUII+U+4j0BWirfSghEybQTQmoYKUGTj755CKYK74ldd3NNQsReoIYsK/ATjQpfaEkB0pC+pCEjdofLkw8paKiMepHCZtOn1hKeW5FR4JnZWCR+0N68ktXWGGF4lqpV5tosH1njZBVvaiJuuLusIBN/fXcTtY26BsZjZwEPG0s5dniG6oaq26gKtCll156rP4hBVWc5L1x6TQ56I8sV38B5ToQPCMgaEm5dWtqMkJPEANrr/Q0GvyBgLUpsxbkdfSMoaKsWbCBo2f3Bf0ikVmY0r1g6RGFQzdSryQqd2gw/bYAtae8uvo8C3uHHXYoMjWOrSOz6u9VuLaTtQ18awFVfeFGece6/OZyUIpiSNW+ITR/42JtvvnmhX/Phx9qgLY/fPLJJy1jORggh4MOOqiY0252H6roCWLgR0477bThoFskLK8Yg8MqrF10nYW+2267tT3Ywx0g/8to82DhPpbFJq2mJC0gWQt/G+xiQlaUSN3CUQ/Kj5EMyLOz2NVrSuhXJ2sbPF/8ABH6t058SEJfuV9N6UrEqg7Ev1SH9GYnDscJxvbnJg4EVAd1GT2j29D1xGDhkNdKYJ1CtJDJTlWF3ATWlrUhOZ1OZIGlhaKJYWXacf6gCpaXVIyeV4LPzuJst912xQKu/24TqkxU/9+OvLYxq1s5pCAoa8OV15HhinGqfanChhzJ2oYqqLumNGUEfUUiakHKrEa7cPHFFxdrLnouyEIpUqNqqQPzHWWmrD/Hs1XwRs/pJvSEYmBNkIMJl2kwUTajAE/pe9sMrIW/kZf1SQELx0nHevvDgQW86qqrhs8DNQtkrv47Ls76CFTVlQ2rhzgE5qLnDBbcCTGGskoPkRq76jXGzKaPCqtKjFRtQx3cgmq/GAFxD+PWpAqByhEojNocCqw9boBxiJ4HYiCnnnpqkZZ0toWCjMrhzbHviAhIdnu8oauJwcId7ACK/pK/9UkpJ4YViu4bCqgFRS1NJwKlLrkI1ZoFG19GIrqexWlnXp46EKQTLedaRJ/Bo1BeeeWVxoi7TTgStQ11cIcuueSSwgIfeeSRRSrwpZdeKlSh4GmkvIBykNmQMozaHSykoX2/InoWeJ7siOtKd0gVK/c2coEQxmmnndbxLNlw0dXEYHEonXWiUkWhxcGSkMnViajC4m8qiOFzO9xSv2eo4PsjoabDUjYk61C9x0Z0Tz2gBmIkAo7Rew0F2kEOgnh9ZTUQF4Kr96cEQh2N7zboe7UmwNjpK3KlwppcRqRhHKM2BwtKT+VoPbtTwtzLsFTvsW6bzoGAWEi3FjaV6GpiYJFFng2wfL6Nxk1QBCO3T5rX70EYJiWaEJadPG1nUYxqt3o6DficJK1FUr1ejAM5NfmgSK16/UgAgQiKNfn0YiCdrm2IQDFC9JuCLwfkov5aL4qf2uWWKZZrKnOXVqYQqtcjs/LTg3UgM5mmdsSSOomuJgYbv0kySg+SwPV7kElfPr/sxj333NNy31ChkrHeRxvJJpOeql5LzSjrbrI+pCfiigiv0+CyGZfRqm0YLBiAJpUjluQshmuiewcLz2lKRyugqxaqgTXhc4LR9WowfLw4xxiGARKyKTAmqOZTYPV7WIkmf7n8FkO7Putmo6thqJfrAsug79WNxMr1FWlHMPzq0ch1s8yjXdsQwQaqqy7wjQmnTiPlxbpfccUVbXPJ+qp69KXyujI599xzwzUBCOaUU04ZFfIfDLqaGCwKsYVogFllg69SzqK2SclhhT1R1Nr13BGZi3bJOAuPVZIlieosymCiWgQpVVasKVCJSPbYY4+2WbmhwPuMZm1DFVJ6xk46GhlVg4nmG8kqbNKnej+dSWnnGRmGROVtFNPwmTmnQcUirCuxA65iU/wDMciojUYKeDDoemLgw5Ow0SCXQTFpOfJMAUlTfIFMV6QSPWc4YN0t4Eg6IgHxBx8UoQaigGOJMvAYPWMkMZDaBoeJOrWw6x+FtcHEa5CDVHNZAyAgHR0OM8Y+i99OqY7YBTub3FqpVJkGR+fFvpxFia7jpiEN2ZVuPzvR1cQAFqpvFxj8+kCzFvx1VY9NVg7K61if6BnDQalWWI2oCIbUtSD0IbJuJViSbvj/PHif/mob+PCkenT/cOC50qs2d1VZISOpX6druWIqWKUx68rQ+PrkvA0atT8cOOHpgFSk+Mwx0hCIbIpFgPhCO+NbnUTXE4OFyorsv//+fR5pboLFY0HJRZvc6BntgEDjUOrp9c+xXSksxVJR2yMNktj71I+523j+5ktY4j/RvUOFZ6qVkHmqPrMOKpGKiGIL4k5UYyf+5z1cE6pBhWrkqvaHWWedtVA97XRxOomuJwawaATxbDzFNlHhSASLx6YTIGp3mWwdpCH/nKRskpIRWD7S2UbrJnlZr23wrQQReDGcTsVBuISscl/KqgliPCoUGZFOBm9llaRJIzemCYgEKRi76nmYbkZPEAPYNCyKKjNWqymIV4Jl8f1+OWiBoajNdkPwjg9MyjrF2FcZLf+ZipGF4IpE7Y0mytoGX1QSUzCONl27ArcRBIZ9xESdSjRmTeDaqG/x//CI2m0nxC6oKfMr+I0wm4iMAUNYiMQ5mG6c5yb0DDGUYFlFdQVxBKj4nAJ8888/f0EYNpwPcPKRVUpGqa5Ow+LhL1MCpLHFQelI9wmkUQkyFD5IUi2X7jZ4Dzl6qcGRWNTIyDPNm/MG1B6CjzYdmG/xBtWxyL+v6s52wvcfkP8555xTpHcpWfMsziCWJUaCTBXn+VBOu9LjI4meI4YSYg8krQ9gUBKqych4/q/FNdp5Yv0jG30pWGWcxS7NJ72qrFtay0aI7u0mjEYfqUPEyh+nVGwwhOrrV4LQNqDzE9SFMwej7YJZa+ZYQNZHdRguH8LhGnV7vUITepYYegWkN58XWbFo/rsXCGG0gViNFaUidlMSqs3m3/L8RHTvaMNcd9LlGglkYsjoeiDSklBtukysnUcmhoyMjBZkYsjIyGhBJoaMjIwWZGLIyMhoQSaGjIyMFmRiyMjIaEEmhoyMjBZkYsjIyGhBJoaMjIwWZGLIyMhoQSaGjIyMFmRiyMjIaEEmhoyMjBZkYsjIyKhhTPofiIMzYod5cF8AAAAASUVORK5CYII=" />
                <input type="text" data-name="captcha" data-key="BQAAAEAAAABueUJwTloyUWhoV0gwYlRwVm96bnFyUEpaTEhoVTJYejZwWjYwZDRLUmhRSTQxRjBPSFZVdnBqS0loUzQzYkxvZlJTRGxtcVltcitvd0JnVTViTW5Rdz09" placeholder="Ketik angka yang ditampilkan di samping">
            </div>
            <div class="errmsg"></div>
        </div>
    </div>
</div>

<script>
    addStrRes('dyn_mobile_example', '812xxxxxxxx');
    addStrRes('mobile_number', 'Nomor HP');
    addStrRes('msg_account_numbers_must_contain_only_numbers', 'Nomor rekening hanya boleh karakter nomor.');
    addStrRes('msg_confirm_password_is_not_match', 'Konfirmasi password salah.');
    addStrRes('msg_digital_wallet_mobile_numbers_must_contain_only_numbers', 'Nomor HP dompet digital hanya boleh karakter nomor.');
    addStrRes('msg_email_already_in_use_please_enter_another_email', 'Email ini sudah digunakan. Harap gunakan email yang lain.');
    addStrRes('msg_html_internal_error_has_occurred_when_trying_to_register_your_account_please_contact_the_support_team_for_further_help_with_the_registration', 'Kesalahan internal telah terjadi ketika mendaftarkan akun anda.&lt;br /&gt;Silahkan hubungi tim support kami untuk bantuan pendaftaran.');
    addStrRes('msg_html_membership_level_is_not_available_for_this_website_please_contact_customer_support_team_to_report_this_problem', 'Level membership tidak tersedia untuk situs ini. &lt;br /&gt;Harap laporkan masalah ini ke tim support Anda.');
    addStrRes('msg_invalid_referral_code', 'Kode referral tidak valid');
    addStrRes('msg_mobile_numbers_must_contain_only_numbers', 'Nomor HP hanya boleh karakter nomor.');
    addStrRes('msg_password_must_between_6_and_12_characters', 'Password harus diantara 6 and 12 karakter.');
    addStrRes('msg_password_must_not_contain_spaces', 'Password tidak boleh ada spasi.');
    addStrRes('msg_phone_credit_numbers_must_contain_only_numbers', 'Nomor pulsa harus hanya berisi angka');
    addStrRes('msg_phone_number_already_in_use_please_enter_another_phone_number', 'Nomor HP ini sudah digunakan. Harap gunakan nomor HP yang lain.');
    addStrRes('msg_please_enter_a_valid_email', 'Harap masukkan alamat email yang valid');
    addStrRes('msg_please_enter_verification_letters', 'Harap masukkan huruf verifikasi');
    addStrRes('msg_please_enter_your_account_name', 'Harap masukkan nama rekening anda');
    addStrRes('msg_please_enter_your_account_number', 'Harap masukkan nomor rekening anda');
    addStrRes('msg_please_enter_your_crypto_wallet_address', 'Silakan masukkan alamat dompet crypto Anda');
    addStrRes('msg_please_enter_your_digital_wallet_account_name', 'Harap masukkan nama dompet digital anda');
    addStrRes('msg_please_enter_your_digital_wallet_mobile_number', 'Harap masukkan nomor HP dompet digital anda');
    addStrRes('msg_please_enter_your_mobile_number', 'Harap masukkan nomor HP Anda di sini');
    addStrRes('msg_please_enter_your_phone_credit_number', 'Silakan masukkan nomor pulsa Anda');
    addStrRes('msg_please_select_banking_options', 'Harap pilih opsi bank');
    addStrRes('msg_please_select_your_bank', 'Silahkan pilih bank anda.');
    addStrRes('msg_please_select_your_banking_account', 'Silahkan pilih bank anda.');
    addStrRes('msg_please_select_your_country', 'Silahkan pilih negara anda sekarang.');
    addStrRes('msg_please_select_your_crypto_wallet', 'Silakan pilih dompet crypto Anda');
    addStrRes('msg_please_select_your_crypto_wallet_network_type', 'Silakan pilih jenis jaringan dompet crypto Anda');
    addStrRes('msg_please_select_your_default_account', 'Harap pilih akun utama anda.');
    addStrRes('msg_please_select_your_digital_wallet', 'Harap pilih dompet digital anda.');
    addStrRes('msg_please_select_your_phone_credit', 'Silakan pilih pulsa Anda');
    addStrRes('msg_please_use_another_bank_account', 'Harap gunakan rekening bank yang lain.');
    addStrRes('msg_please_use_another_bank_or_digital_wallet_account', 'Silahkan pakai bank lain atau akun dompet digital');
    addStrRes('msg_please_use_another_crypto_wallet_address', 'Harap gunakan alamat dompet crypto lainnya');
    addStrRes('msg_please_use_another_digital_wallet_account', 'Harap gunakan rekening dompet digital yang lain.');
    addStrRes('msg_please_use_another_phone_credit_number', 'Harap gunakan nomor pulsa lainnya');
    addStrRes('msg_the_bank_account_is_already_registered', 'Rekening bank sudah terdaftar.');
    addStrRes('msg_the_bank_and_digital_wallet_account_must_not_be_the_same', 'Bank dan akun dompet digital tidak boleh sama');
    addStrRes('msg_the_crypto_wallet_address_is_already_registered', 'Alamat dompet crypto sudah terdaftar');
    addStrRes('msg_the_digital_wallet_account_is_already_registered', 'Rekening dompet digital sudah terdaftar.');
    addStrRes('msg_the_phone_credit_number_is_already_registered', 'Nomor pulsa sudah terdaftar');
    addStrRes('msg_the_referral_code_xxx_can_be_used_for_currency_xxx_only', 'Kode referral {0} hanya bisa dipake untuk mata uang {1}');
    addStrRes('msg_the_verification_letters_entered_is_not_valid', 'Huruf verifikasi yang dimasukkan tidak valid.');
    addStrRes('msg_username_already_exists', 'Username sudah digunakan.');
    addStrRes('msg_username_already_exists_please_select_another_username', 'Username sudah digunakan. Silahkan pilih username yang lain.');
    addStrRes('msg_username_must_between_3_and_8_characters', 'Username harus diantara 3 and 8 karakter.');
    addStrRes('msg_username_must_contain_only_alphabets_and_numbers', 'Username hanya boleh karakter alfabet dan nomor.');
    addStrRes('registration', 'Pendaftaran');
    addStrRes('update_bank_options', 'Perbarui Opsi Bank');
    addStrRes('update_phone_code', 'Perbarui Code HP');

    const CHKEMAIL = false;
    const CHKPHONE = false;
    const USEBANKLIST = true;
</script>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-robotic btn-primary" onclick="regUser(this.parentElement, '/Member/SOptPage?opt=RegistrationSuccessfulPage');">Daftar</button>
                                <button type="button" class="btn btn-robotic btn-primary" data-dismiss="modal">Batal</button>
                            </div>
                        </div>
                    </div>
                </div>

            <div id="pageLoading" class="container-loader circle96">
                <div data-name="msg"></div>
            </div>

            <div id="sgaDlg" class="modal fade" data-dtype="info">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" data-dtext="title"></h4>
            </div>
            <div class="modal-body">
                <div class="modal-ico"></div>
                <div class="modal-msg" data-dtext="msg"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-btype="yes">Ya</button>
                <button type="button" class="btn btn-danger" data-btype="no">Tidak</button>
                <button type="button" class="btn btn-primary text-uppercase" data-dismiss="modal" data-btype="ok">Ok</button>
                <button type="button" class="btn btn-danger" data-btype="cancel">Batal</button>
            </div>
        </div>
    </div>
</div>

<script>
    addStrRes('cancel', 'Batal');
    addStrRes('no', 'Tidak');
    addStrRes('ok', 'Ok');
    addStrRes('yes', 'Ya');
</script>
            <div id="sgaDlgProg" class="modal fade" role="dialog" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            </div>
            <div class="modal-body">
                <div class="text-label"></div>
                <div class="progress" style="position: relative;">
                    <div class="progress-bar progress-bar-striped indeterminate"></div>
                </div>
            </div>
        </div>
    </div>
</div>
            

    
    <div class="pagepopup"></div>
    <div class="pagepopup-main">
        <div data-name="close">X</div>
        <div data-name="pagecontent">
                    <img class="pagepopupimg" data-name="1" src="https://sga-cdn-hxg6b2d7ctb2c0eu.z02.azurefd.net/agent-websites/286/medialibrary/images/286_4e580914102c4734a7bf82caed32d0dd.png" alt="Deposit QRIS MICROVIP88" title="Popup.png" />
        </div>
    </div>

    <script>
    addStrRes('error_code', 'Kode Kesalahan');
    addStrRes('msg_html_database_timeout_has_occurred_when_trying_to_perform_the_requested_action', 'Waktu operasi database telah melewati batas yang ditentukan untuk melakukan tindakan ini.');
    addStrRes('msg_html_if_problem_persists_please_report_this_error_to_the_support_team', 'Jika masalah tetap ada, harap laporkan masalah ini ke tim support anda.');
    addStrRes('msg_html_internal_database_error_has_occurred_please_try_it_again_shortly', 'Kesalahan internal terhadap basis data telah terjadi. Silakan coba sebentar lagi.');
    addStrRes('msg_html_this_function_is_not_supported_please_contact_the_support_team_to_report_this_error', 'Fungsi ini tidak tersedia. Harap laporkan masalah ini ke tim support anda.');
    addStrRes('msg_html_you_have_no_authority_to_perform_this_action', 'Anda tidak memiliki wewenang untuk melakukan tindakan ini!');
    addStrRes('msg_internal_error_on_passing_data_to_the_server_has_occurred', 'Kesalahan internal telah terjadi ketika mengirimkan data ke server.');
    addStrRes('msg_please_check_your_connection', 'Harap periksa koneksi internet Anda.');
    addStrRes('msg_please_login_again', 'Silakan login lagi.');
    addStrRes('msg_please_reload_your_page', 'Silakan muat ulang halaman Anda.');
    addStrRes('msg_please_try_it_again_later', 'Silakan coba lagi nanti.');
    addStrRes('msg_server_internal_error_has_occurred_when_trying_to_perform_the_task', 'Kesalahan internal telah terjadi di server ketika mencoba melakukan tugas ini.');
    addStrRes('msg_server_operation_is_in_progress', 'Operasi server sedang berlangsung.');
    addStrRes('msg_the_record_already_exists_in_the_database', 'Data ini sudah tersedia di database.');
    addStrRes('msg_the_record_is_no_longer_exists_in_the_database', 'Data ini sudah tidak tersedia lagi di database.');
    addStrRes('msg_the_server_is_not_responding', 'Tidak ada respon dari server.');
    addStrRes('msg_there_is_problem_with_your_internet_connection', 'Kami mendeteksi masalah dengan koneksi internet Anda.');
    addStrRes('msg_unable_to_perform_the_task_because_the_request_has_exceeded_server_maximum_byte_size', 'Tidak dapat melakukan tugas karena permintaan telah melebihi ukuran byte maksimal server.');
    addStrRes('msg_unable_to_perform_the_task_because_your_access_has_been_restricted_to_a_certain_ip_address_and_or_a_certain_time', 'Tidak dapat melakukan tugas karena akses Anda telah dibatasi ke alamat IP tertentu dan/atau waktu tertentu.');
    addStrRes('msg_unable_to_perform_the_task_because_your_login_has_expired', 'Tidak dapat melakukan tugas karena info masuk Anda telah kedaluwarsa.');
    addStrRes('msg_unable_to_save_this_record_because_some_fields_requirement_are_not_met', 'Tidak dapat menyimpan data ini karena beberapa persyaratan field tidak terpenuhi.');
    addStrRes('msg_unhandled_error_has_occurred_when_performing_the_task', 'Kesalahan tidak tertangani telah terjadi saat melakukan tugas.');
    addStrRes('msg_your_browser_session_has_expired_we_will_reload_your_browser_to_refresh_your_session', 'Sesi browser Anda sudah berakhir. Kami akan memuat ulang browser Anda untuk menyegarkan sesi Anda.');
</script>
    <script>
    addStrRes('user_status_active', 'Aktif');
    addStrRes('user_status_locked', 'Dikunci');
    addStrRes('user_status_rejected', 'Ditolak');
    addStrRes('user_status_suspended', 'Suspend');
    addStrRes('user_status_waiting_for_approval', 'Menunggu Persetujuan');
</script>

    <script>
        addStrRes('hot_games', 'Hot Games');
        addStrRes('load_hot_games', 'Muat Hot Games');
        addStrRes('load_page_slides', 'Muat Slide Halaman');
        addStrRes('page_slides', 'Slide Halaman');
        addStrRes('play_now', 'Main Sekarang');
        addStrRes('under_maintenance', 'Dalam Perbaikan');

        $(document).ready(function () {
            SGACounter.countUp($('.jackpot-content [data-name="jackpot"]'), *********, 812589991, 500, 604800, 30, true, function (n) { return SGAGlobal.format(n, 'n0'); });
                    });
    </script>

<script src="/Contents/Scripts/htmlbody/js?v=YvE3NZpZaY4e4kuumNLT8hxDiJH19o6YKNCNTGbgYZg1"></script>
<script src="/Contents/Scripts/htmlbody/state/loggedout/theme/robotic/js?v=8t0CKerMaUhka-sThVm47fUKPBk_8YfYYZ2sH4F69AA1"></script>

            <script>
                addStrRes('error_code', 'Kode Kesalahan');
                addStrRes('internal_error', 'Kesalahan Internal');
                addStrRes('load_providers', 'Muat Provider');
                addStrRes('login', 'Masuk');
                addStrRes('msg_an_internal_error_has_occurred_on_the_scripting_code', 'Kesalahan internal telah terjadi didalam scripting code.');
                addStrRes('msg_failed_to_load_xxx_please_try_again', 'Gagal memuat {0}. Silakan coba lagi.');
                addStrRes('msg_html_invalid_session_please_try_again_in_xxx_minutes', 'Sesi tidak valid. &lt;br /&gt;Silakan coba lagi dalam &lt;b&gt;{0}&lt;/b&gt; menit.');
                addStrRes('msg_loading_xxx_please_wait', 'Sedang memuat {0}, harap ditunggu...');
                addStrRes('msg_please_contact_your_support_team_for_further_help', 'Silahkan menghubungi tim support untuk mendapatkan bantuan lebih lanjut.');
                addStrRes('msg_please_report_this_error_to_the_support_team', 'Harap laporkan masalah ini ke tim support anda.');
                addStrRes('msg_please_wait', 'Harap ditunggu.');
                addStrRes('new', 'Baru');
                addStrRes('providers', 'Provider');
                addStrRes('refresh', 'Segarkan');
                addStrRes('under_maintenance', 'Dalam Perbaikan');
                                
                addStrRes('msg_html_it_seems_like_there_is_another_active_login_using_this_browser_for_security_reasons_we_have_logged_out_all_users_from_this_browser_please_sign_in_again', 'Sepertinya ada login aktif lain yang menggunakan browser ini. Demi keamanan, kami telah mengeluarkan semua pengguna dari browser ini.&lt;br /&gt;&lt;br /&gt;Silakan login untuk masuk kembali lagi.');
                addStrRes('msg_invalid_username_or_password', 'Login anda salah!');
                addStrRes('msg_login_to_xxx', 'Login ke Microvip88');
                addStrRes('msg_please_enter_your_password', 'Harap masukkan password Anda.');
                addStrRes('msg_please_enter_your_username', 'Harap masukkan username Anda.');
                addStrRes('msg_please_login_to_your_member_account_to_play_this_game', 'Silakan masuk ke akun Anda untuk memainkan game ini.');
                addStrRes('msg_your_account_has_been_locked', 'Akun Anda telah dikunci.');
                addStrRes('play_game', 'Main Game');
                

                const HASLOGIN = false;
                const LANG_CULTURE = 'id-ID';
                const MAX_BYTES = 5242880;
            </script>

            <!-- Start of LiveChat (www.livechat.com) code -->
<script>
    window.__lc = window.__lc || {};
    window.__lc.license = ********;
    window.__lc.integration_name = "manual_channels";
    window.__lc.product_name = "livechat";
    ;(function(n,t,c){function i(n){return e._h?e._h.apply(null,n):e._q.push(n)}var e={_q:[],_h:null,_v:"2.0",on:function(){i(["on",c.call(arguments)])},once:function(){i(["once",c.call(arguments)])},off:function(){i(["off",c.call(arguments)])},get:function(){if(!e._h)throw new Error("[LiveChatWidget] You can't use getters before load.");return i(["get",c.call(arguments)])},call:function(){i(["call",c.call(arguments)])},init:function(){var n=t.createElement("script");n.async=!0,n.type="text/javascript",n.src="https://cdn.livechatinc.com/tracking.js",t.head.appendChild(n)}};!n.__lc.asyncInit&&e.init(),n.LiveChatWidget=n.LiveChatWidget||e}(window,document,[].slice))
</script>
<noscript><a href="https://www.livechat.com/chat-with/********/" rel="nofollow">Chat with us</a>, powered by <a href="https://www.livechat.com/?welcome" rel="noopener nofollow" target="_blank">LiveChat</a></noscript>
<!-- End of LiveChat code -->

        </body>
        </html>
